/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    optimizePackageImports: ["lucide-react"],
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.pexels.com",
      },
    ],
  },
  async rewrites() {
    return [
      // Handle app subdomain rewrites
      {
        source: "/app/:path*",
        destination: "/app/:path*",
        has: [
          {
            type: "host",
            value: "app.lightquant.io",
          },
        ],
      },
    ];
  },
};

export default nextConfig;
