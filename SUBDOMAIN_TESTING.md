# Subdomain Testing Guide

## Local Development Setup

To test the subdomain routing locally, you need to set up local domain mapping.

### Option 1: Using /etc/hosts (Recommended for testing)

1. Edit your hosts file:
   ```bash
   sudo nano /etc/hosts
   ```

2. Add these lines:
   ```
   127.0.0.1 app.localhost
   127.0.0.1 localhost
   ```

3. Save and exit

### Option 2: Direct URL Testing

You can also test by directly visiting:
- Main site: `http://localhost:3001`
- App subdomain: `http://app.localhost:3001`

## Testing the Implementation

1. **Start the development server:**
   ```bash
   npm run dev
   ```

2. **Test main site:**
   - Visit: `http://localhost:3001`
   - Should show the main LightQuant landing page
   - Click the "Login" button - should redirect to app subdomain

3. **Test app subdomain:**
   - Visit: `http://app.localhost:3001`
   - Should show the app dashboard
   - Visit: `http://app.localhost:3001/login`
   - Should show the login form

4. **Test middleware protection:**
   - Try visiting: `http://localhost:3001/app`
   - Should redirect to main page (middleware protection)

## Production URLs

- Main site: `https://lightquant.io`
- App subdomain: `https://app.lightquant.io`

## How It Works

1. **Middleware Detection:** The middleware detects the subdomain from the `host` header
2. **Route Rewriting:** For `app.` subdomain, routes are rewritten to `/app/*` paths
3. **Protection:** Main domain cannot access `/app` routes directly
4. **Login Redirect:** Login buttons detect environment and redirect appropriately

## File Structure

```
/
├── middleware.ts              # Subdomain routing logic
├── app/
│   ├── layout.tsx            # Main site layout
│   ├── page.tsx              # Main site homepage
│   ├── components/Header.tsx # Header with smart login redirect
│   └── app/                  # App subdomain routes
│       ├── layout.tsx        # App-specific layout
│       ├── page.tsx          # App dashboard
│       └── login/
│           └── page.tsx      # Login form
```
