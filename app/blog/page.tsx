import { getAllPosts } from "@/lib/blog";
import BlogListClient from "./BlogListClient";

const BlogPage = async () => {
  const posts = await getAllPosts();
  if (posts.length === 0) {
    return <p className="text-white">No posts found.</p>;
  }
  return (
    <>
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-black relative overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-20" />
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-blue-600/20 to-transparent rounded-full blur-3xl animate-float" />
        <div className="relative z-10 max-w-7xl mx-auto px-6 text-center">
          <h1 className="font-heading text-4xl md:text-6xl font-bold text-white mb-6 animate-fade-in-up">
            Crypto Trading Insights, Bot Strategies & AI Tips
          </h1>
          <p className="font-body text-xl text-gray-400 max-w-3xl mx-auto animate-fade-in-up delay-200">
            Stay ahead of the market with in-depth articles on automated trading, AI strategies,
            platform updates, and more.
          </p>
        </div>
      </section>

      <div className="bg-gray-900 min-h-screen">
        <div className="max-w-7xl mx-auto px-6 py-16">
          <BlogListClient posts={posts} />
          {/* Bottom CTA */}
          <div className="mt-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-12 text-center text-white">
            <h2 className="font-heading text-3xl font-bold mb-4">
              Ready to put what you've learned into action?
            </h2>
            <p className="font-body text-xl mb-8 opacity-90">
              Start your 7-day free trial and let AI trade for you.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                className="bg-white text-blue-600 px-8 py-4 rounded-xl font-body font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105"
                type="button"
              >
                Try a Bot Now
              </button>
              <button
                className="border-2 border-white text-white px-8 py-4 rounded-xl font-body font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300"
                type="button"
              >
                Browse All Bots
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BlogPage;
