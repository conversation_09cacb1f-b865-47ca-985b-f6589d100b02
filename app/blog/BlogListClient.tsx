"use client";

import { ArrowRight, Search, Tag, TrendingUp, User } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import type { PostMeta } from "@/lib/blog";
import { formatDate } from "@/lib/date";

interface BlogListClientProps {
  posts: PostMeta[];
}

const BlogListClient = ({ posts }: BlogListClientProps) => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredPosts = posts.filter((post) => {
    const term = searchTerm.toLowerCase();
    return post.title.toLowerCase().includes(term) || post.excerpt.toLowerCase().includes(term);
  });

  const popularTags = Array.from(new Set(posts.flatMap((p) => p.tags)).values()).slice(0, 5);

  return (
    <div className="grid lg:grid-cols-4 gap-8">
      {/* Main Content */}
      <div className="lg:col-span-3">
        {/* Blog Grid */}
        <div className="grid md:grid-cols-2 gap-8">
          {filteredPosts.map((post, index) => (
            <Link
              key={post.slug}
              href={`/blog/${post.slug}`}
              className="group block bg-black border border-gray-800 rounded-2xl shadow-lg hover:border-gray-700 hover:shadow-2xl transition-all duration-500 overflow-hidden transform hover:-translate-y-2 animate-scale-in"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="relative overflow-hidden">
                <img
                  src={post.thumbnail}
                  alt={post.title}
                  className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-700"
                />
              </div>
              <div className="p-6">
                <h3 className="font-heading text-xl font-bold text-white mb-3 group-hover:text-blue-400 transition-colors line-clamp-2">
                  {post.title}
                </h3>
                <p className="font-body text-gray-400 mb-4 line-clamp-3 leading-relaxed">
                  {post.excerpt}
                </p>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-white">{post.author}</p>
                      <p className="text-xs text-gray-400">{formatDate(post.date)}</p>
                    </div>
                  </div>
                  <span className="text-sm text-gray-400">{post.readTime}</span>
                </div>
                <div className="group/btn inline-flex items-center gap-2 text-blue-400 font-semibold">
                  Read More
                  <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Sidebar */}
      <div className="lg:col-span-1">
        <div className="sticky top-24 space-y-8">
          {/* Search */}
          <div className="bg-black border border-gray-800 rounded-2xl p-6 shadow-lg">
            <h3 className="font-bold text-white mb-4 flex items-center gap-2">
              <Search className="w-5 h-5 text-blue-400" />
              Search Articles
            </h3>
            <div className="relative">
              <input
                type="text"
                placeholder="Search blog posts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-3 bg-gray-900 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
              />
              <Search className="absolute right-3 top-3.5 w-4 h-4 text-gray-400" />
            </div>
          </div>

          {/* Popular Tags */}
          <div className="bg-black border border-gray-800 rounded-2xl p-6 shadow-lg">
            <h3 className="font-bold text-white mb-4 flex items-center gap-2">
              <Tag className="w-5 h-5 text-blue-400" />
              Popular Tags
            </h3>
            <div className="flex flex-wrap gap-2">
              {popularTags.map((tag) => (
                <span
                  key={tag}
                  className="px-3 py-1 bg-gray-800 text-gray-300 rounded-full text-sm hover:bg-blue-500/20 hover:text-blue-400 transition-colors cursor-pointer border border-gray-700"
                >
                  #{tag}
                </span>
              ))}
            </div>
          </div>

          {/* Recent Posts */}
          <div className="bg-black border border-gray-800 rounded-2xl p-6 shadow-lg">
            <h3 className="font-bold text-white mb-4 flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-blue-400" />
              Recent Posts
            </h3>
            <div className="space-y-4">
              {posts.slice(0, 3).map((post) => (
                <Link key={post.slug} href={`/blog/${post.slug}`} className="block group">
                  <h4 className="font-medium text-white group-hover:text-blue-400 transition-colors line-clamp-2 mb-1">
                    {post.title}
                  </h4>
                  <p className="text-sm text-gray-400">{formatDate(post.date)}</p>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogListClient;
