"use client";

import { Menu, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();

  // Function to check if a link is active (including child pages)
  const isLinkActive = (href: string) => {
    if (href === "/") {
      return pathname === "/";
    }
    return pathname?.startsWith(href);
  };

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Lock body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isMobileMenuOpen]);

  const navLinks = [
    { name: "Home", href: "/" },
    { name: "Market", href: "/market" },
    { name: "Blog", href: "/blog" },
    { name: "About Us", href: "/about" },
  ];

  return (
    <>
      <header
        className={`fixed top-0 left-0 right-0 z-[100] transition-all duration-500 ${
          isScrolled && !isMobileMenuOpen
            ? "bg-black/95 backdrop-blur-md shadow-2xl border-b border-gray-800"
            : "bg-transparent"
        }`}
      >
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex items-center justify-between h-20">
            {/* Logo */}
            <div className="flex items-center">
              <Link href="/" className="flex items-center gap-3 group">
                <div className="relative">
                  <Image
                    src="/logo.png"
                    alt="LightQuant Logo"
                    width={200}
                    height={40}
                    className="filter brightness-0 invert"
                  />
                </div>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              {navLinks.map((link) => {
                const isActive = isLinkActive(link.href);
                return (
                  <Link
                    key={link.name}
                    href={link.href}
                    className={`relative font-body font-medium transition-all duration-300 group ${
                      isActive ? "text-blue-400" : "text-gray-300 hover:text-white"
                    }`}
                  >
                    {link.name}
                    {isActive && (
                      <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full" />
                    )}
                    <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full transition-all duration-300 scale-x-0 group-hover:scale-x-100" />
                  </Link>
                );
              })}
            </nav>

            {/* Login Button & Mobile Menu */}
            <div className="flex items-center gap-4">
              <button
                onClick={() => {
                  // For development, use localhost; for production, use app.lightquant.io
                  const isLocalhost =
                    window.location.hostname === "localhost" ||
                    window.location.hostname === "127.0.0.1";
                  const appUrl = isLocalhost
                    ? `http://app.localhost:${window.location.port}/login`
                    : "https://app.lightquant.io/login";
                  window.location.href = appUrl;
                }}
                className="hidden md:block bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-500 hover:from-blue-500 hover:via-purple-500 hover:to-cyan-400 text-white px-6 py-2.5 rounded-xl font-body font-semibold transition-all duration-300 transform hover:scale-105 glow-blue hover:shadow-lg"
                type="button"
              >
                Login
              </button>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="md:hidden p-2 rounded-lg text-gray-300 hover:text-white hover:bg-gray-800 transition-colors duration-300"
                type="button"
              >
                {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Menu - Full Screen Overlay */}
      <div
        className={`md:hidden fixed top-0 left-0 w-full h-full z-[200] transition-all duration-500 ease-in-out ${
          isMobileMenuOpen ? "opacity-100 visible" : "opacity-0 invisible"
        }`}
      >
        {/* Background Overlay */}
        <div className="absolute inset-0 bg-black" />

        {/* Menu Content */}
        <div className="relative h-full flex flex-col">
          {/* Header with Close Button */}
          <div className="flex items-center justify-between h-20 px-6 border-b border-gray-800/50">
            <div className="flex items-center">
              <Image
                src="/logo.png"
                alt="LightQuant Logo"
                width={200}
                height={40}
                className="filter brightness-0 invert"
              />
            </div>
            <button
              onClick={() => setIsMobileMenuOpen(false)}
              className="p-2 rounded-lg text-gray-300 hover:text-white hover:bg-gray-800/50 transition-all duration-300"
              type="button"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Navigation Links */}
          <div className="flex-1 flex flex-col justify-center px-6">
            <nav className="space-y-8">
              {navLinks.map((link, index) => {
                const isActive = isLinkActive(link.href);
                return (
                  <Link
                    key={link.name}
                    href={link.href}
                    className={`block text-4xl font-heading font-bold transition-all duration-300 transform hover:translate-x-2 ${
                      isActive ? "text-white" : "text-gray-400 hover:text-white"
                    }`}
                    style={{
                      animationDelay: `${index * 100}ms`,
                    }}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {link.name}
                    {isActive && (
                      <div className="w-12 h-1 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full mt-2" />
                    )}
                  </Link>
                );
              })}
            </nav>
          </div>

          {/* Bottom Section with Login Button */}
          <div className="p-6 border-t border-gray-800/50">
            <button
              className="w-full bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-500 hover:from-blue-500 hover:via-purple-500 hover:to-cyan-400 text-white px-8 py-4 rounded-xl font-body font-semibold text-lg transition-all duration-300 transform hover:scale-105 glow-blue"
              type="button"
              onClick={() => {
                setIsMobileMenuOpen(false);
                // For development, use localhost; for production, use app.lightquant.io
                const isLocalhost =
                  window.location.hostname === "localhost" ||
                  window.location.hostname === "127.0.0.1";
                const appUrl = isLocalhost
                  ? `http://app.localhost:${window.location.port}/login`
                  : "https://app.lightquant.io/login";
                window.location.href = appUrl;
              }}
            >
              Login
            </button>

            {/* Social Links */}
            {/* <div className="mt-6 flex justify-center space-x-6">
                <a
                  href="https://facebook.com/LightQuant"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-12 h-12 rounded-full border border-gray-700 flex items-center justify-center text-gray-400 hover:text-white hover:border-blue-400 transition-all duration-300 text-sm font-semibold"
                  aria-label="Facebook"
                >
                  <Facebook className="w-6 h-6" />
                </a>
                <a
                  href="https://linkedin.com/company/LightQuant"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-12 h-12 rounded-full border border-gray-700 flex items-center justify-center text-gray-400 hover:text-white hover:border-blue-400 transition-all duration-300 text-sm font-semibold"
                  aria-label="LinkedIn"
                >
                  <Linkedin className="w-6 h-6" />
                </a>
              </div> */}

            <div className="mt-6 text-center">
              <p className="text-gray-500 text-sm font-body">
                © 2025 LightQuant. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Header;
