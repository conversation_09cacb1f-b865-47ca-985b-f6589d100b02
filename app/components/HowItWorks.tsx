import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "lucide-react";

const HowItWorks = () => {
  const steps = [
    {
      step: "1",
      icon: Link2,
      title: "Connect Your Exchange",
      description: "Link your Binance, Bybit, or OKX via secure API.",
      color: "text-blue-400",
      bgColor: "bg-blue-500/10",
      borderColor: "border-blue-500/30",
      gradient: "from-blue-500 to-cyan-500",
    },
    {
      step: "2",
      icon: Bo<PERSON>,
      title: "Choose a Bot",
      description: "Filter bots by strategy, risk level, or performance.",
      color: "text-purple-400",
      bgColor: "bg-purple-500/10",
      borderColor: "border-purple-500/30",
      gradient: "from-purple-500 to-pink-500",
    },
    {
      step: "3",
      icon: Monitor,
      title: "Monitor and Optimize",
      description: "Let the bot trade. Track results live and adjust anytime.",
      color: "text-amber-400",
      bgColor: "bg-amber-500/10",
      borderColor: "border-amber-500/30",
      gradient: "from-amber-500 to-orange-500",
    },
  ];

  return (
    <section className="py-20 bg-black">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="font-heading text-4xl md:text-5xl font-bold text-white mb-4 animate-fade-in-up">
            ⚙️ Get Started in 3 Simple Steps
          </h2>
        </div>

        <div className="grid md:grid-cols-3 gap-8 mb-12">
          {steps.map((step, index) => {
            const Icon = step.icon;
            return (
              <div
                // biome-ignore lint/suspicious/noArrayIndexKey: step order is predetermined
                key={index}
                className={`group relative bg-gray-900/50 rounded-3xl p-8 border ${step.borderColor} hover:bg-gray-800/50 hover:border-opacity-60 transition-all duration-500 transform animate-scale-in`}
                style={{ animationDelay: `${index * 200}ms` }}
              >
                {/* Step Number */}
                <div
                  className={`absolute -top-4 left-8 w-8 h-8 ${step.bgColor} ${step.color} rounded-full flex items-center justify-center font-bold text-lg border ${step.borderColor} bg-black group-hover:scale-110 transition-transform duration-300`}
                >
                  {step.step}
                </div>

                <div
                  className={`w-16 h-16 ${step.bgColor} border ${step.borderColor} rounded-2xl flex items-center justify-center mb-6 mt-4 group-hover:scale-110 transition-transform duration-300`}
                >
                  <Icon className={`w-8 h-8 ${step.color}`} />
                </div>

                <h3 className="font-heading text-xl font-bold text-white mb-3">{step.title}</h3>

                <p className="font-body text-gray-400 leading-relaxed">{step.description}</p>

                {/* Connector Arrow */}
                {index < steps.length - 1 && (
                  <div className="hidden md:block absolute top-1/2 -right-9 transform -translate-y-1/2">
                    <div className="w-9 h-0.5 bg-gray-700" />
                    <div className="absolute -right-4 -top-1 w-0 h-0 border-l-4 border-l-gray-700 border-t-2 border-b-2 border-t-transparent border-b-transparent" />
                  </div>
                )}
              </div>
            );
          })}
        </div>

        <div className="text-center">
          <button
            className="bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-500 hover:from-blue-500 hover:via-purple-500 hover:to-cyan-400 text-white px-8 py-4 rounded-xl font-body font-semibold text-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105 glow-blue flex items-center gap-2 mx-auto"
            type="button"
          >
            👉 View Bot Marketplace
            <ArrowRight className="w-5 h-5" />
          </button>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
