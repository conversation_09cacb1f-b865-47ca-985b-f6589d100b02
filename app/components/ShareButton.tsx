"use client";

import { Share2 } from "lucide-react";
import { useState } from "react";

const ShareButton = () => {
  const [copied, setCopied] = useState(false);

  const handleShare = async () => {
    const url = window.location.href;
    try {
      if (navigator.share) {
        await navigator.share({ url });
      } else if (navigator.clipboard) {
        await navigator.clipboard.writeText(url);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      }
    } catch {
      // ignore errors (user canceled, unsupported, etc.)
    }
  };

  return (
    <button
      onClick={handleShare}
      className="p-2 bg-gray-800 text-blue-400 rounded-lg hover:bg-blue-500/20 transition-colors"
      type="button"
    >
      {copied ? <span className="text-xs">Copied!</span> : <Share2 className="w-4 h-4" />}
    </button>
  );
};

export default ShareButton;
