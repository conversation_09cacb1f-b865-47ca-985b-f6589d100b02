import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Play,
  Shield,
  TrendingUp,
  Zap,
} from "lucide-react";

const Hero = () => {
  return (
    <section className="relative min-h-screen bg-black overflow-hidden">
      {/* Dynamic Animated Background */}
      <div className="absolute inset-0">
        {/* Animated Matrix Grid */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-cyan-500/10 animate-pulse" />
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
                linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px)
              `,
              backgroundSize: "40px 40px",
              animation: "matrix-scroll 20s linear infinite",
            }}
          />
        </div>

        {/* Floating Orbs with Complex Animations */}
        <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-r from-blue-500/30 to-cyan-500/30 rounded-full blur-xl animate-float-complex opacity-60" />
        <div className="absolute top-40 right-32 w-24 h-24 bg-gradient-to-r from-purple-500/40 to-pink-500/40 rounded-full blur-xl animate-float-complex delay-1000 opacity-50" />
        <div className="absolute bottom-32 left-40 w-40 h-40 bg-gradient-to-r from-amber-500/20 to-orange-500/20 rounded-full blur-2xl animate-float-complex delay-2000 opacity-40" />
        <div className="absolute top-60 right-20 w-16 h-16 bg-gradient-to-r from-green-500/50 to-emerald-500/50 rounded-full blur-lg animate-float-complex delay-3000 opacity-70" />

        {/* Animated Particles */}
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className={
              "absolute w-1 h-1 bg-blue-400 rounded-full animate-particle-drift opacity-60"
            }
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: `${3 + Math.random() * 4}s`,
            }}
          />
        ))}

        {/* Dynamic Gradient Waves */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-500/50 to-transparent animate-wave-slide" />
          <div className="absolute top-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-purple-500/40 to-transparent animate-wave-slide delay-1000" />
          <div className="absolute bottom-1/3 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyan-500/30 to-transparent animate-wave-slide delay-2000" />
        </div>

        {/* Rotating Geometric Shapes */}
        <div className="absolute top-1/3 right-1/4 w-20 h-20 border border-blue-500/20 rotate-45 animate-spin-slow" />
        <div className="absolute bottom-1/3 left-1/4 w-16 h-16 border border-purple-500/20 animate-spin-reverse" />
        <div className="absolute top-2/3 right-1/3 w-12 h-12 border border-cyan-500/20 rotate-12 animate-spin-slow delay-1000" />
      </div>

      {/* Interactive Trading Chart Visualization */}
      <div className="absolute top-1/4 right-10 w-80 h-48 opacity-20 hidden lg:block">
        <div className="relative w-full h-full">
          {/* Animated Chart Lines */}
          <svg className="w-full h-full" viewBox="0 0 320 192">
            <defs>
              <linearGradient id="chartGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.8" />
                <stop offset="50%" stopColor="#8B5CF6" stopOpacity="0.6" />
                <stop offset="100%" stopColor="#06B6D4" stopOpacity="0.4" />
              </linearGradient>
            </defs>
            <path
              d="M0,150 Q80,120 160,100 T320,80"
              stroke="url(#chartGradient)"
              strokeWidth="3"
              fill="none"
              className="animate-chart-draw"
            />
            <path
              d="M0,160 Q80,140 160,130 T320,110"
              stroke="#10B981"
              strokeWidth="2"
              fill="none"
              opacity="0.6"
              className="animate-chart-draw delay-500"
            />
          </svg>

          {/* Floating Data Points */}
          <div className="absolute top-8 left-16 w-2 h-2 bg-green-400 rounded-full animate-pulse" />
          <div className="absolute top-12 right-20 w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse delay-300" />
          <div className="absolute bottom-16 left-1/3 w-1 h-1 bg-purple-400 rounded-full animate-pulse delay-600" />
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-6 pt-32 pb-16">
        <div className="text-center">
          {/* Animated Status Badge */}
          <div className="mb-8 animate-fade-in-up">
            <div className="inline-flex items-center gap-3 bg-gradient-to-r from-gray-900/90 to-black/90 backdrop-blur-xl border border-gray-800/50 rounded-full px-6 py-3 mb-8 shadow-2xl hover:scale-105 transition-transform duration-300 cursor-pointer">
              <div className="relative">
                <Zap className="w-5 h-5 text-blue-400 animate-pulse" />
                <div className="absolute inset-0 w-5 h-5 bg-blue-400/20 rounded-full animate-ping" />
              </div>
              <span className="text-gray-300 text-sm font-medium tracking-wide">
                AI-POWERED TRADING REVOLUTION
              </span>
              <div className="flex gap-1">
                <div className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse" />
                <div className="w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse delay-200" />
                <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-pulse delay-400" />
              </div>
            </div>
          </div>

          {/* Dynamic Typography with Glitch Effect */}
          <div className="mb-8">
            <h1 className="text-6xl md:text-8xl font-black text-white mb-4 leading-none animate-fade-in-up delay-200 tracking-tight relative">
              <span className="relative inline-block group">
                <span className="bg-gradient-to-r from-white via-gray-100 to-white bg-clip-text text-transparent group-hover:animate-glitch">
                  AI-POWERED
                </span>
                <div className="absolute -inset-2 bg-gradient-to-r from-blue-600/20 to-purple-600/20 blur-xl opacity-50 animate-pulse group-hover:opacity-80 transition-opacity duration-300" />
              </span>
            </h1>

            <h2 className="text-5xl md:text-7xl font-black mb-6 animate-fade-in-up delay-300 tracking-tight relative group cursor-pointer">
              <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent animate-gradient bg-300 leading-none group-hover:scale-105 transition-transform duration-300 inline-block">
                CRYPTO TRADING BOTS
              </span>
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-0 h-1 bg-gradient-to-r from-blue-500 to-purple-500 group-hover:w-full transition-all duration-500" />
            </h2>
          </div>

          {/* Interactive Tagline */}
          <div className="mb-8 animate-fade-in-up delay-500">
            <p className="text-2xl md:text-3xl text-gray-300 mb-4 font-light tracking-wide hover:text-white transition-colors duration-300 cursor-pointer">
              Smarter. Faster. Fully Automated.
            </p>
            <div className="flex items-center justify-center gap-4 text-gray-500">
              <div className="w-16 h-px bg-gradient-to-r from-transparent to-gray-500 animate-pulse" />
              <span className="text-sm font-medium tracking-widest uppercase bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Premium AI Trading
              </span>
              <div className="w-16 h-px bg-gradient-to-l from-transparent to-gray-500 animate-pulse" />
            </div>
          </div>

          {/* Enhanced Description with Hover Effects */}
          <p className="text-xl text-gray-400 mb-12 max-w-4xl mx-auto leading-relaxed animate-fade-in-up delay-600 font-light hover:text-gray-300 transition-colors duration-300">
            Discover intelligent crypto trading bots trained on real market data.
            <span className="text-white font-medium hover:bg-gradient-to-r hover:from-blue-400 hover:to-purple-400 hover:bg-clip-text hover:text-transparent transition-all duration-300 cursor-pointer">
              Optimize profits, reduce risk, and automate your strategy
            </span>{" "}
            — all in one click.
          </p>

          {/* Premium Interactive CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16 animate-fade-in-up delay-700">
            <button
              className="group relative bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-500 hover:from-blue-500 hover:via-purple-500 hover:to-cyan-400 text-white px-10 py-5 rounded-2xl font-bold text-xl transition-all duration-500 transform hover:scale-110 shadow-2xl hover:shadow-blue-500/25 overflow-hidden"
              type="button"
            >
              {/* Button Background Animation */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-cyan-500 rounded-2xl blur opacity-30 group-hover:opacity-50 transition-opacity duration-500 animate-pulse" />

              {/* Shimmer Effect */}
              <div className="absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/10 to-transparent group-hover:animate-shimmer" />

              <span className="relative flex items-center gap-3">
                <div className="relative">
                  <Play className="w-6 h-6 fill-current group-hover:scale-110 transition-transform duration-300" />
                  <div className="absolute inset-0 bg-white/20 rounded-full animate-ping opacity-0 group-hover:opacity-100" />
                </div>
                Start Free Trial – 7 Days
                <ArrowRight className="w-6 h-6 group-hover:translate-x-2 group-hover:scale-110 transition-all duration-300" />
              </span>
            </button>

            <button
              className="group relative border-2 border-gray-700 hover:border-gray-500 text-gray-300 hover:text-white px-10 py-5 rounded-2xl font-bold text-xl transition-all duration-500 transform hover:scale-105 backdrop-blur-sm hover:bg-gray-800/30 overflow-hidden"
              type="button"
            >
              {/* Hover Background */}
              <div className="absolute inset-0 bg-gradient-to-r from-gray-800/0 via-gray-700/20 to-gray-800/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

              <span className="relative flex items-center gap-3">
                <BarChart3 className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
                View Bot Performance
              </span>
            </button>
          </div>

          {/* Interactive Feature Cards with Hover Animations */}
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[
              {
                icon: TrendingUp,
                title: "AI-Driven Intelligence",
                description: "Personalized bots adapted to your risk profile",
                color: "text-blue-400",
                bgGradient: "from-blue-500/10 to-cyan-500/5",
                borderGradient: "from-blue-500/30 to-cyan-500/30",
                delay: "delay-800",
                hoverColor: "hover:from-blue-500/20 hover:to-cyan-500/10",
              },
              {
                icon: Shield,
                title: "Enterprise Security",
                description: "Bank-grade API integration with major exchanges",
                color: "text-amber-400",
                bgGradient: "from-amber-500/10 to-orange-500/5",
                borderGradient: "from-amber-500/30 to-orange-500/30",
                delay: "delay-900",
                hoverColor: "hover:from-amber-500/20 hover:to-orange-500/10",
              },
              {
                icon: BarChart3,
                title: "Real-Time Analytics",
                description: "Transparent performance with live insights",
                color: "text-purple-400",
                bgGradient: "from-purple-500/10 to-pink-500/5",
                borderGradient: "from-purple-500/30 to-pink-500/30",
                delay: "delay-1000",
                hoverColor: "hover:from-purple-500/20 hover:to-pink-500/10",
              },
            ].map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div
                  key={index}
                  className={`group relative bg-gradient-to-br ${feature.bgGradient} ${feature.hoverColor} backdrop-blur-xl border border-gray-800/50 hover:border-gray-700/70 rounded-3xl p-8 transition-all duration-700 transform hover:-translate-y-4 hover:rotate-1 animate-scale-in ${feature.delay} shadow-2xl hover:shadow-xl cursor-pointer overflow-hidden`}
                >
                  {/* Gradient Border Effect */}
                  <div
                    className={`absolute inset-0 bg-gradient-to-r ${feature.borderGradient} rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm`}
                  />

                  {/* Animated Background Pattern */}
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-500">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-slide-right" />
                  </div>

                  <div className="relative">
                    <div className="relative mb-6 group-hover:animate-bounce">
                      <Icon
                        className={`w-10 h-10 ${feature.color} mx-auto group-hover:scale-125 transition-transform duration-500`}
                      />
                      <div
                        className={`absolute inset-0 w-10 h-10 ${feature.color.replace("text-", "bg-")}/20 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500 mx-auto animate-pulse`}
                      />

                      {/* Floating Particles */}
                      <div className="absolute -top-2 -right-2 w-1 h-1 bg-current rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-ping" />
                      <div className="absolute -bottom-2 -left-2 w-1 h-1 bg-current rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-ping delay-200" />
                    </div>

                    <h3 className="text-lg font-bold text-white mb-3 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-white group-hover:to-gray-300 group-hover:bg-clip-text transition-all duration-500">
                      {feature.title}
                    </h3>

                    <p className="text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors duration-500">
                      {feature.description}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Enhanced Trust Indicators */}
          <div className="mt-20 animate-fade-in-up delay-1100">
            <div className="flex flex-wrap justify-center items-center gap-6 text-gray-500 text-sm">
              {[
                { icon: Lock, text: "SSL Encrypted", color: "bg-green-400" },
                { icon: Zap, text: "7-Day Free Trial", color: "bg-blue-400" },
                { icon: CreditCard, text: "No Credit Card Required", color: "bg-purple-400" },
              ].map((item, index) => (
                <div
                  key={index}
                  className="group flex items-center gap-3 bg-gray-900/50 hover:bg-gray-800/70 backdrop-blur-sm border border-gray-800/50 hover:border-gray-700/70 rounded-full px-6 py-3 transition-all duration-300 hover:scale-105 cursor-pointer"
                >
                  <div
                    className={`w-2 h-2 ${item.color} rounded-full animate-pulse group-hover:animate-bounce`}
                    style={{ animationDelay: `${index * 500}ms` }}
                  />
                  <span className="font-medium group-hover:text-white transition-colors duration-300">
                    <item.icon className="inline w-4 h-4 mr-1" /> {item.text}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Gradient Fade */}
      <div className="absolute bottom-0 left-0 right-0 h-40 bg-gradient-to-t from-gray-900 via-gray-900/50 to-transparent" />
    </section>
  );
};

export default Hero;
