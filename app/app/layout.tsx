import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "../globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "LightQuant App - AI-Powered Crypto Trading Platform",
  description:
    "Access your LightQuant trading dashboard. Manage your AI-powered crypto trading bots, monitor performance, and optimize your trading strategies.",
  icons: {
    icon: "/favicon.png",
  },
};

export default function AppLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <div className="min-h-screen bg-black">
          {children}
        </div>
      </body>
    </html>
  );
}
