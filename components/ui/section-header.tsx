import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const sectionHeaderVariants = cva("text-center mb-16", {
  variants: {
    animation: {
      none: "",
      "fade-in-up": "animate-fade-in-up",
    },
  },
  defaultVariants: {
    animation: "fade-in-up",
  },
});

const sectionTitleVariants = cva(
  "font-heading font-bold text-white mb-4 animate-fade-in-up",
  {
    variants: {
      size: {
        sm: "text-2xl md:text-3xl",
        default: "text-4xl md:text-5xl",
        lg: "text-5xl md:text-6xl",
        xl: "text-6xl md:text-7xl",
      },
    },
    defaultVariants: {
      size: "default",
    },
  }
);

const sectionDescriptionVariants = cva(
  "font-body text-gray-400 max-w-3xl mx-auto animate-fade-in-up delay-200",
  {
    variants: {
      size: {
        sm: "text-base",
        default: "text-xl",
        lg: "text-2xl",
      },
    },
    defaultVariants: {
      size: "default",
    },
  }
);

export interface SectionHeaderProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof sectionHeaderVariants> {
  title: string;
  description?: string;
  titleSize?: VariantProps<typeof sectionTitleVariants>["size"];
  descriptionSize?: VariantProps<typeof sectionDescriptionVariants>["size"];
}

const SectionHeader = React.forwardRef<HTMLDivElement, SectionHeaderProps>(
  (
    {
      className,
      animation,
      title,
      description,
      titleSize,
      descriptionSize,
      ...props
    },
    ref
  ) => {
    return (
      <div
        ref={ref}
        className={cn(sectionHeaderVariants({ animation, className }))}
        {...props}
      >
        <h2 className={cn(sectionTitleVariants({ size: titleSize }))}>
          {title}
        </h2>
        {description && (
          <p className={cn(sectionDescriptionVariants({ size: descriptionSize }))}>
            {description}
          </p>
        )}
      </div>
    );
  }
);
SectionHeader.displayName = "SectionHeader";

export { SectionHeader, sectionHeaderVariants };
