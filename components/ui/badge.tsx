import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary: "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive: "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        // LightQuant specific variants
        blue: "border-blue-500/30 bg-blue-500/10 text-blue-400 hover:bg-blue-500/20",
        purple: "border-purple-500/30 bg-purple-500/10 text-purple-400 hover:bg-purple-500/20",
        amber: "border-amber-500/30 bg-amber-500/10 text-amber-400 hover:bg-amber-500/20",
        green: "border-green-500/30 bg-green-500/10 text-green-400 hover:bg-green-500/20",
        cyan: "border-cyan-500/30 bg-cyan-500/10 text-cyan-400 hover:bg-cyan-500/20",
        pink: "border-pink-500/30 bg-pink-500/10 text-pink-400 hover:bg-pink-500/20",
        // Gradient variants
        "gradient-blue": "border-transparent bg-gradient-to-r from-blue-500 to-cyan-500 text-white hover:from-blue-600 hover:to-cyan-600",
        "gradient-purple": "border-transparent bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600",
        "gradient-amber": "border-transparent bg-gradient-to-r from-amber-500 to-orange-500 text-white hover:from-amber-600 hover:to-orange-600",
        // Status variants
        success: "border-green-500/30 bg-green-500/10 text-green-400",
        warning: "border-amber-500/30 bg-amber-500/10 text-amber-400",
        error: "border-red-500/30 bg-red-500/10 text-red-400",
        info: "border-blue-500/30 bg-blue-500/10 text-blue-400",
      },
      size: {
        default: "px-2.5 py-0.5 text-xs",
        sm: "px-2 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
        xl: "px-4 py-1.5 text-base",
      },
      animation: {
        none: "",
        pulse: "animate-pulse",
        bounce: "animate-bounce",
        glow: "animate-premium-glow",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      animation: "none",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, size, animation, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant, size, animation }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
