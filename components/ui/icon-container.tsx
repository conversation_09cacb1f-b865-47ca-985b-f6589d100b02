import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const iconContainerVariants = cva(
  "flex items-center justify-center rounded-2xl border transition-all duration-300 group-hover:scale-110",
  {
    variants: {
      variant: {
        blue: "bg-blue-500/10 border-blue-500/30 text-blue-400",
        purple: "bg-purple-500/10 border-purple-500/30 text-purple-400",
        amber: "bg-amber-500/10 border-amber-500/30 text-amber-400",
        green: "bg-green-500/10 border-green-500/30 text-green-400",
        cyan: "bg-cyan-500/10 border-cyan-500/30 text-cyan-400",
        red: "bg-red-500/10 border-red-500/30 text-red-400",
        gray: "bg-gray-500/10 border-gray-500/30 text-gray-400",
      },
      size: {
        sm: "w-12 h-12",
        default: "w-16 h-16",
        lg: "w-20 h-20",
        xl: "w-24 h-24",
      },
      glow: {
        none: "",
        subtle: "shadow-lg",
        medium: "shadow-xl",
        strong: "shadow-2xl",
      },
    },
    defaultVariants: {
      variant: "blue",
      size: "default",
      glow: "none",
    },
  }
);

const iconVariants = cva("transition-all duration-300", {
  variants: {
    size: {
      sm: "w-6 h-6",
      default: "w-8 h-8",
      lg: "w-10 h-10",
      xl: "w-12 h-12",
    },
  },
  defaultVariants: {
    size: "default",
  },
});

export interface IconContainerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof iconContainerVariants> {
  icon: React.ComponentType<{ className?: string }>;
  iconSize?: VariantProps<typeof iconVariants>["size"];
}

const IconContainer = React.forwardRef<HTMLDivElement, IconContainerProps>(
  ({ className, variant, size, glow, icon: Icon, iconSize, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(iconContainerVariants({ variant, size, glow, className }))}
        {...props}
      >
        <Icon className={cn(iconVariants({ size: iconSize || size }))} />
      </div>
    );
  }
);
IconContainer.displayName = "IconContainer";

export { IconContainer, iconContainerVariants };
