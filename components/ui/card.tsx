import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const cardVariants = cva(
  "group relative rounded-3xl border transition-all duration-500 transform",
  {
    variants: {
      variant: {
        default: "bg-black border-gray-800/50 hover:bg-gray-800/50 hover:border-gray-700/70",
        secondary:
          "bg-gray-900/50 border-gray-800/50 hover:bg-gray-800/50 hover:border-gray-700/70",
        feature: "bg-gradient-to-br backdrop-blur-xl border-gray-800/50 hover:border-gray-700/70",
        glass: "bg-gray-900/90 backdrop-blur-xl border-gray-800/50 hover:border-gray-700/70",
      },
      size: {
        default: "p-8",
        sm: "p-6",
        lg: "p-10",
        xl: "p-12",
      },
      hover: {
        none: "",
        lift: "hover:-translate-y-2",
        float: "hover:-translate-y-4",
        scale: "hover:scale-105",
        rotate: "hover:rotate-1",
      },
      animation: {
        none: "",
        "scale-in": "animate-scale-in",
        "fade-in-up": "animate-fade-in-up",
        "slide-in-left": "animate-slide-in-left",
        "slide-in-right": "animate-slide-in-right",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      hover: "lift",
      animation: "scale-in",
    },
  }
);

const cardHeaderVariants = cva("flex flex-col space-y-1.5", {
  variants: {
    align: {
      left: "text-left",
      center: "text-center items-center",
      right: "text-right items-end",
    },
  },
  defaultVariants: {
    align: "left",
  },
});

const cardTitleVariants = cva("font-heading font-bold text-white leading-none tracking-tight", {
  variants: {
    size: {
      sm: "text-lg",
      default: "text-2xl",
      lg: "text-3xl",
      xl: "text-4xl",
    },
  },
  defaultVariants: {
    size: "default",
  },
});

const cardDescriptionVariants = cva("font-body text-gray-400 leading-relaxed", {
  variants: {
    size: {
      sm: "text-sm",
      default: "text-base",
      lg: "text-lg",
    },
  },
  defaultVariants: {
    size: "default",
  },
});

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  asChild?: boolean;
}

export interface CardHeaderProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardHeaderVariants> {}

export interface CardTitleProps
  extends React.HTMLAttributes<HTMLHeadingElement>,
    VariantProps<typeof cardTitleVariants> {}

export interface CardDescriptionProps
  extends React.HTMLAttributes<HTMLParagraphElement>,
    VariantProps<typeof cardDescriptionVariants> {}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, size, hover, animation, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(cardVariants({ variant, size, hover, animation, className }))}
      {...props}
    />
  )
);
Card.displayName = "Card";

const CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, align, ...props }, ref) => (
    <div ref={ref} className={cn(cardHeaderVariants({ align, className }))} {...props} />
  )
);
CardHeader.displayName = "CardHeader";

const CardTitle = React.forwardRef<HTMLParagraphElement, CardTitleProps>(
  ({ className, size, ...props }, ref) => (
    <h3 ref={ref} className={cn(cardTitleVariants({ size, className }))} {...props} />
  )
);
CardTitle.displayName = "CardTitle";

const CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(
  ({ className, size, ...props }, ref) => (
    <p ref={ref} className={cn(cardDescriptionVariants({ size, className }))} {...props} />
  )
);
CardDescription.displayName = "CardDescription";

const CardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => <div ref={ref} className={cn("pt-0", className)} {...props} />
);
CardContent.displayName = "CardContent";

const CardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn("flex items-center pt-6", className)} {...props} />
  )
);
CardFooter.displayName = "CardFooter";

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };
