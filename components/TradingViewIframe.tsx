"use client";

import { useId, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { COINS } from "@/utils/coinList";

export default function TradingViewIframe() {
  const [symbol, setSymbol] = useState(COINS[0].tv);
  const id = useId(); // unique <iframe> id
  const encoded = encodeURIComponent(symbol);
  const src =
    `https://s.tradingview.com/widgetembed/?symbol=${encoded}` +
    "&interval=60&theme=dark&style=1&timezone=Etc%2FUTC" +
    "&hide_side_toolbar=0&withdateranges=1&saveimage=0&studies=[]" +
    `&frameElementId=${id}`;

  const selectedCoin = COINS.find((coin) => coin.tv === symbol);

  console.log(COINS)

  return (
    <div className="w-full max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl md:text-5xl font-heading font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent mb-4">
          Market Charts
        </h1>
        <p className="text-gray-400 text-lg">Real-time cryptocurrency market data</p>
      </div>

      {/* Coin Selector */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
        <label
          htmlFor="crypto-select"
          className="text-sm font-medium text-gray-300 whitespace-nowrap"
        >
          Select Asset:
        </label>
        <Select value={symbol} onValueChange={setSymbol}>
          <SelectTrigger
            id="crypto-select"
            className="w-full sm:w-80 h-12 rounded-xl border-gray-700 bg-gray-900/50 text-white hover:bg-gray-800/50 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300"
          >
            <SelectValue placeholder="Select a cryptocurrency" />
          </SelectTrigger>
          <SelectContent className="bg-gray-900 border-gray-700 rounded-xl">
            {COINS.map(({ label, tv }) => (
              <SelectItem
                key={tv}
                value={tv}
                className="text-white hover:bg-gray-800 focus:bg-gray-800 focus:text-white rounded-lg"
              >
                {label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* TradingView Chart */}
      <div className="relative rounded-2xl overflow-hidden border border-gray-800/50 bg-gray-900/20 backdrop-blur-sm shadow-2xl">
        {/* Chart Header */}
        <div className="px-6 py-4 border-b border-gray-800/50 bg-gray-900/30">
          <h3 className="text-lg font-heading font-semibold text-white">
            {selectedCoin?.label || "Cryptocurrency"} Chart
          </h3>
        </div>

        {/* Chart Iframe */}
        <iframe
          key={symbol} /* forces re‑mount on change */
          id={id}
          src={src}
          width="100%"
          height="600"
          allowFullScreen
          className="w-full bg-black border-0 overflow-hidden"
          title={`TradingView Chart for ${selectedCoin?.label || "Cryptocurrency"}`}
        />
      </div>
    </div>
  );
}
