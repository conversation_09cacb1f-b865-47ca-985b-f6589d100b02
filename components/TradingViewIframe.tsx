"use client";

import { useId, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { COINS } from "@/utils/coinList";

export default function TradingViewIframe() {
  const [symbol, setSymbol] = useState(COINS[0].tv);
  const id = useId(); // unique <iframe> id
  const encoded = encodeURIComponent(symbol);
  const src =
    `https://s.tradingview.com/widgetembed/?symbol=${encoded}` +
    "&interval=60&theme=dark&style=1&timezone=Etc%2FUTC" +
    "&hide_side_toolbar=0&withdateranges=1&saveimage=0&studies=[]" +
    `&frameElementId=${id}`;

  return (
    <div className="w-full max-w-6xl mx-auto">
      {/* Coin Selector */}
      <div className="mb-6">
        <label htmlFor="crypto-select" className="block text-sm font-medium text-gray-300 mb-3">
          Select Cryptocurrency
        </label>
        <Select value={symbol} onValueChange={setSymbol}>
          <SelectTrigger
            id="crypto-select"
            className="w-80 bg-gray-900/50 border-gray-700 text-white hover:bg-gray-800/50 focus:ring-blue-500 focus:border-blue-500"
          >
            <SelectValue placeholder="Select a cryptocurrency" />
          </SelectTrigger>
          <SelectContent className="bg-gray-900 border-gray-700">
            {COINS.map(({ label, tv }) => (
              <SelectItem
                key={tv}
                value={tv}
                className="text-white hover:bg-gray-800 focus:bg-gray-800 focus:text-white"
              >
                {label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* TradingView Chart */}
      <div className="relative rounded-2xl overflow-hidden border border-gray-800/50 bg-gray-900/20 backdrop-blur-sm">
        <iframe
          key={symbol} /* forces re‑mount on change */
          id={id}
          src={src}
          width="100%"
          height="600"
          frameBorder="0"
          scrolling="no"
          allowFullScreen
          className="w-full"
          title={`TradingView Chart for ${COINS.find((coin) => coin.tv === symbol)?.label || "Cryptocurrency"}`}
        />
      </div>
    </div>
  );
}
