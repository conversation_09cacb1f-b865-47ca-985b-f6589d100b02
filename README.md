# Lightquant

This project uses Next.js with the App Router. To run the development server:

```bash
pnpm install
pnpm dev
```

Before running, copy `.env.example` to `.env` and provide the required database, NextAuth and wallet variables.

## Database Setup

Prisma is used for the PostgreSQL schema. After configuring `DATABASE_URL` in your `.env`, generate the client and apply the migrations:

```bash
pnpm db:generate
pnpm db:migrate
```

This will create the necessary tables in your database.

## Authentication

NextAuth.js handles authentication with Google, Facebook and a credentials provider. Register new accounts via `POST /api/auth/register` with `email`, `password` and optional `name`. Then sign in through `/api/auth/[...nextauth]` using your credentials or social providers. Configure provider keys in `.env` as shown in `.env.example`.
