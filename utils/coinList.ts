// utils/coinList.ts
export const COINS = [
  { label: 'Bitcoin (BTC)', tv: 'BINANCE:BTCUSDT' },
  { label: 'Ethereum (ETH)', tv: 'BINANCE:ETHUSDT' },
  { label: 'BNB (BNB)', tv: 'BINANCE:BNBUSDT' },
  { label: 'Solana (SOL)', tv: 'BINANCE:SOLUSDT' },
  { label: 'XRP (XRP)', tv: 'BINANCE:XRPUSDT' },
  { label: '<PERSON><PERSON><PERSON><PERSON> (DOGE)', tv: 'BINANCE:DOGEUSDT' },
  { label: 'Toncoin (TON)', tv: 'BINANCE:TONUSDT' },
  { label: 'Cardano (ADA)', tv: 'BINANCE:ADAUSDT' },
  { label: 'Avalanche (AVAX)', tv: 'BINANCE:AVAXUSDT' },
  { label: '<PERSON>ba Inu (SHIB)', tv: 'BINANCE:SHIBUSDT' },
  { label: 'TRON (TRX)', tv: 'BINANCE:TRXUSDT' },
  { label: 'Polkadot (DOT)', tv: 'BINANCE:DOTUSDT' },
  { label: 'Polygon (MATIC)', tv: 'BINANCE:MATICUSDT' },
  { label: 'Litecoin (LTC)', tv: 'BINANCE:LTCUSDT' },
  { label: 'Bitcoin Cash (BCH)', tv: 'BINANCE:BCHUSDT' },
  { label: 'Uniswap (UNI)', tv: 'BINANCE:UNIUSDT' },
  { label: 'Chainlink (LINK)', tv: 'BINANCE:LINKUSDT' },
  { label: 'Stellar (XLM)', tv: 'BINANCE:XLMUSDT' },
  { label: 'Sui (SUI)', tv: 'BINANCE:SUIUSDT' },
  { label: 'Ethereum Classic (ETC)', tv: 'BINANCE:ETCUSDT' },
];
