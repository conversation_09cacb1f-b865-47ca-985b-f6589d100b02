import fs from "node:fs/promises";
import path from "node:path";
import matter from "gray-matter";
import { remark } from "remark";
import html from "remark-html";

export interface PostMeta {
  title: string;
  slug: string;
  date: string;
  thumbnail?: string;
  author?: string;
  readTime?: string;
  tags: string[];
  excerpt: string;
}

export interface Post extends PostMeta {
  content: string;
}

const postsDirectory = path.join(process.cwd(), "content/posts");

function extractExcerpt(content: string): string {
  const text = content.replace(/\r?\n/g, " ");
  return `${text.split(" ").slice(0, 30).join(" ").trim()}...`;
}

export async function getAllPosts(): Promise<PostMeta[]> {
  const files = await fs.readdir(postsDirectory);
  const posts: PostMeta[] = [];

  for (const file of files) {
    if (!file.endsWith(".md")) continue;
    const fullPath = path.join(postsDirectory, file);
    const fileContents = await fs.readFile(fullPath, "utf8");
    const { data, content } = matter(fileContents);
    if (data.draft) continue;
    const slug = data.slug ?? file.replace(/\.md$/, "");
    posts.push({
      title: data.title ?? slug,
      slug,
      date: data.date,
      thumbnail: data.thumbnail ?? "/logo.png",
      author: data.author ?? "",
      readTime: data.readTime ?? "",
      tags: Array.isArray(data.tags) ? data.tags : [],
      excerpt: extractExcerpt(content),
    });
  }

  posts.sort((a, b) => (a.date > b.date ? -1 : 1));
  return posts;
}

export async function getPostBySlug(slug: string): Promise<Post> {
  const files = await fs.readdir(postsDirectory);

  for (const file of files) {
    if (!file.endsWith(".md")) continue;
    const fullPath = path.join(postsDirectory, file);
    const fileContents = await fs.readFile(fullPath, "utf8");
    const { data, content } = matter(fileContents);
    const fileSlug = data.slug ?? file.replace(/\.md$/, "");

    if (fileSlug === slug || file.replace(/\.md$/, "") === slug) {
      const processed = await remark().use(html).process(content);
      const contentHtml = processed.toString();

      return {
        title: data.title ?? fileSlug,
        slug: fileSlug,
        date: data.date,
        thumbnail: data.thumbnail ?? "/logo.png",
        author: data.author ?? "",
        readTime: data.readTime ?? "",
        tags: Array.isArray(data.tags) ? data.tags : [],
        excerpt: extractExcerpt(content),
        content: contentHtml,
      };
    }
  }

  throw new Error(`Post not found: ${slug}`);
}
