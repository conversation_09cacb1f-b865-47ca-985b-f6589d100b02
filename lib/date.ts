import { format as formatDateFns, formatDistanceToNow, parseISO } from "date-fns";

function toISODateString(date: string | Date): string {
  if (typeof date === "string") return date;
  if (date instanceof Date && !Number.isNaN(date.getTime())) return date.toISOString();
  return String(date);
}

export function formatRelativeDate(dateInput: string | Date): string {
  const dateStr = toISODateString(dateInput);
  let date: Date;
  try {
    date = parseISO(dateStr);
  } catch {
    return dateStr;
  }
  if (Number.isNaN(date.getTime())) return dateStr;
  return formatDistanceToNow(date, { addSuffix: true });
}

export function formatDate(dateInput: string | Date): string {
  const dateStr = toISODateString(dateInput);
  let date: Date;
  try {
    date = parseISO(dateStr);
  } catch {
    return dateStr;
  }
  if (Number.isNaN(date.getTime())) return dateStr;
  return formatDateFns(date, "dd MMM yyyy");
}
