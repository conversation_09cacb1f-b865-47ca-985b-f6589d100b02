import { type NextRequest, NextResponse } from "next/server";

export function middleware(request: NextRequest) {
  const url = request.nextUrl.clone();
  const hostname = request.headers.get("host") || "";

  // Extract subdomain
  const subdomain = hostname.split(".")[0];

  // Handle app subdomain (including localhost for development)
  if (subdomain === "app" || hostname.startsWith("app.localhost")) {
    // Rewrite to /app routes
    if (url.pathname === "/") {
      url.pathname = "/app";
    } else if (!url.pathname.startsWith("/app")) {
      url.pathname = `/app${url.pathname}`;
    }

    return NextResponse.rewrite(url);
  }

  // Handle main domain - ensure we don't serve app routes
  if (url.pathname.startsWith("/app") && !hostname.startsWith("app.")) {
    return NextResponse.redirect(new URL("/", request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)",
  ],
};
