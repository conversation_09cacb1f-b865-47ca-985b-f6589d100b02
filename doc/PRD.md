# Lightquant Product Requirements Document

## Overview
Lightquant aims to provide a platform where users can follow automated trading bots on major cryptocurrency exchanges. The current repository contains only the frontend landing page. This PRD describes the initial backend services that will power user accounts, wallet management, API token storage, and bot-following logic using Next.js API routes.

## Goals
- Allow users to register and authenticate via email, Google, or Facebook.
- Provide each user with a unique crypto wallet address controlled by Lightquant.
- Track deposits and withdrawals securely using an internal balance ledger.
- Support subscription and performance-based payment plans.
- Let users store multiple exchange API tokens (sub‑accounts) to follow trading bots.
- Enable following and unfollowing bots with strict capital tracking.
- Record trades and compute daily profit for user dashboards.

## User Accounts
- Users sign up or log in using email, Google, or Facebook OAuth.
- On account creation, backend generates a dedicated wallet address for the user. The private key is held by Lightquant.
- Each account has an internal virtual balance used for withdrawals and plan payments.

## Wallet Management
1. **Deposits**
   - Users send USDT to their personal wallet address.
   - Backend monitors on‑chain transactions, credits the corresponding amount to the user’s virtual balance, then moves the funds to the Lightquant wallet.
2. **Withdrawals**
   - User submits a withdrawal request.
   - System verifies that the amount is ≤ current virtual balance and ≤ total historical deposits to that wallet.
   - If the check fails, the request is rejected and the account may be locked.
   - Approved withdrawals are paid from the Lightquant wallet to the user.

## Payment Plans
- Lightquant offers subscription plans and performance-based plans.
- Plan fees are deducted from the user’s virtual balance.

## Exchange API Tokens
- Users may link multiple API tokens from exchanges such as Binance or OKX.
- A token represents one sub-account on the exchange.
- Each token can simultaneously follow **one spot bot** and **one futures/margin bot**.
- API keys are stored securely (encrypted at rest).

## Bots and Following Logic
- Bots trade on a single exchange and are categorized by type (spot, futures, AI, etc.). Initially the focus is on Binance bots.
- When a user chooses to follow a bot they must specify the capital allocation for that bot and select an exchange API token.
- The backend checks the exchange via API to ensure sufficient balance on that token.
- During trading, the engine tracks the allocation separately from the user’s total exchange balance. All trades operate on a percentage of the allocated amount (either current balance or initial amount depending on the bot settings).
- If the user stops following, the engine closes all bot-created positions/orders.

## Trade Tracking and Reporting
- Every trade executed on behalf of a user is recorded with timestamp, pair, size, price and resulting balance.
- The engine calculates current profit/loss daily and updates the user dashboard.
- Accurate history is required because a user may deposit or withdraw independently on the exchange; using sub-accounts is recommended to avoid manual trading interference.

## Technology Stack
- **Backend**: Next.js API routes written in TypeScript.
- **Database**: relational database (e.g. PostgreSQL) for users, wallets, API tokens, bots, and trades.
- **Authentication**: NextAuth or a similar provider for email and social logins.
- **Crypto Monitoring**: service to detect deposits on user wallets and trigger balance updates.
- **Task Scheduling**: background jobs for trade execution and daily profit calculations.
- **Package Manager**: pnpm (see repository guidelines).

## Security Considerations
- Private keys for user wallets are controlled by Lightquant and must be stored securely.
- Withdrawal logic double-checks against total deposit history to prevent fraud.
- API keys should be encrypted and never exposed to the client.
- When suspicious withdrawal activity is detected, the account is locked for investigation.

## Future Work
- Integrate additional exchanges beyond Binance.
- Implement a full trading engine capable of multiple strategies.
- Add unit and integration tests for all backend functionality.

