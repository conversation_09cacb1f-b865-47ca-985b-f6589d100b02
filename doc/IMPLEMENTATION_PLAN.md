# Implementation Plan for Lightquant Backend

This plan details the tasks needed to implement the backend described in `doc/PRD.md`. The platform uses Next.js API routes and PostgreSQL. Every step should be executed using pnpm scripts.

## Task 1: Project Setup
- **1.1** Prepare environment variables for database credentials, encryption keys, OAuth providers and Lightquant wallet secrets.
- **1.2** Enable TypeScript-based API routes in the existing Next.js project.
- **1.3** Configure `pnpm` scripts for dev server, builds, lint and format.
- **1.4** Add base NextAuth configuration and secure session management.

## Task 2: Database Schema
- **2.1** Install Prisma and connect to PostgreSQL.
- **2.2** Define models: `User`, `Wallet`, `LedgerEntry`, `ApiToken`, `Bot`, `Follow`, `Plan`, `PlanSubscription`.
- **2.3** Create migrations and bootstrap the database.

## Task 3: Authentication
- **3.1** Integrate email, Google and Facebook login via NextAuth.
- **3.2** Save user profiles and OAuth details in the database.
- **3.3** Provide API routes for sign in, sign out and session retrieval.

## Task 4: Wallet Service
- **4.1** At user registration, generate a unique crypto wallet and store its encrypted private key.
- **4.2** Monitor blockchain deposits to user wallets.
- **4.3** Credit deposits to the user's virtual balance and transfer funds to the Lightquant wallet.
- **4.4** Record each deposit as a ledger entry linked to the user.

## Task 5: Withdrawals
- **5.1** API route to submit withdrawal requests.
- **5.2** Validate requests against the virtual balance and total deposit history.
- **5.3** Queue the payout from the Lightquant wallet and record a ledger entry.
- **5.4** Automatically lock the account on suspicious requests.

## Task 6: Payment Plans
- **6.1** Store available subscription and performance plans.
- **6.2** Create plan subscription records for each user.
- **6.3** Deduct subscription fees from the virtual balance on schedule.

## Task 7: Exchange API Tokens
- **7.1** Endpoints to add, update and delete exchange API keys.
- **7.2** Encrypt keys at rest and validate them with the exchange.
- **7.3** Ensure each token follows at most one spot bot and one futures/margin bot.

## Task 8: Bot Following
- **8.1** Endpoint to list bots and let a user follow or unfollow.
- **8.2** When following, verify the user's exchange balance via API and store the allocated amount.
- **8.3** On unfollow, call the external trading API to close any open positions.
- **8.4** Keep a record of active follows per user and bot.

## Task 9: External Trading API Integration
- **9.1** Implement services to fetch trade history and profit data from the external API.
- **9.2** Store the returned trades for each user to display on dashboards.
- **9.3** Expose API routes that return the stored trade and profit information.

## Task 10: Security, Testing and Monitoring
- **10.1** Encrypt private keys, API tokens and other sensitive data.
- **10.2** Validate and sanitize all API input and add rate limiting.
- **10.3** Log and alert on failed withdrawals or suspicious events.
- **10.4** Write unit and integration tests for core services and routes.
- **10.5** Run lint and tests in CI for every commit.
