datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id        String   @id @default(uuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  wallets   Wallet[]
  apiTokens ApiToken[]
  ledgerEntries LedgerEntry[]
}

// User exchange account

model Wallet {
  id        String   @id @default(uuid())
  user      User     @relation(fields: [userId], references: [id])
  userId    String
  address   String   @unique
  encryptedPrivateKey String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model LedgerEntry {
  id        String   @id @default(uuid())
  user      User     @relation(fields: [userId], references: [id])
  userId    String
  amount    Decimal  @db.Decimal(18, 8)
  type      LedgerType
  txHash    String?
  createdAt DateTime @default(now())
}

enum LedgerType {
  DEPOSIT
  WITHDRAWAL
  FEE
  SUBSCRIPTION
}

model ApiToken {
  id        String   @id @default(uuid())
  user      User     @relation(fields: [userId], references: [id])
  userId    String
  exchange  String
  apiKey    String
  apiSecret String
  type      TokenType
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  follows   Follow[]
}

enum TokenType {
  SPOT
  FUTURES
}

model Bot {
  id        String   @id @default(uuid())
  name      String
  exchange  String
  type      BotType
  active    Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  follows   Follow[]
}

enum BotType {
  SPOT
  FUTURES
  AI
}

model Follow {
  id           String   @id @default(uuid())
  user         User     @relation(fields: [userId], references: [id])
  userId       String
  bot          Bot      @relation(fields: [botId], references: [id])
  botId        String
  apiToken     ApiToken @relation(fields: [apiTokenId], references: [id])
  apiTokenId   String
  allocatedAmount Decimal @db.Decimal(18, 8)
  createdAt    DateTime @default(now())
  status       FollowStatus @default(ACTIVE)
}

enum FollowStatus {
  ACTIVE
  STOPPED
}

model Plan {
  id        String   @id @default(uuid())
  name      String
  price     Decimal  @db.Decimal(18, 8)
  period    PlanPeriod
  type      PlanType
  active    Boolean  @default(true)
  subscriptions PlanSubscription[]
}

enum PlanPeriod {
  MONTHLY
  YEARLY
}

enum PlanType {
  SUBSCRIPTION
  PERFORMANCE
}

model PlanSubscription {
  id        String   @id @default(uuid())
  user      User     @relation(fields: [userId], references: [id])
  userId    String
  plan      Plan     @relation(fields: [planId], references: [id])
  planId    String
  startDate DateTime @default(now())
  endDate   DateTime?
  status    SubscriptionStatus @default(ACTIVE)
}

enum SubscriptionStatus {
  ACTIVE
  CANCELLED
  EXPIRED
}
